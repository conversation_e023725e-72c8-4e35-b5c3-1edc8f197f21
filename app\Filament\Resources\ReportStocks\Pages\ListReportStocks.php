<?php

namespace App\Filament\Resources\ReportStocks\Pages;

use App\Filament\Resources\ReportStocks\ReportStockResource;
use Filament\Resources\Pages\ListRecords;
use Filament\Notifications\Notification;
use Filament\Actions\Action;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\FileUpload;
use App\Imports\ReportStockImport;
use App\Imports\ReportStockImportSimple;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Support\Facades\Storage;

class ListReportStocks extends ListRecords
{
    protected static string $resource = ReportStockResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Action::make('import_report_stock')
                ->label('Import Excel')
                ->icon('heroicon-o-arrow-up-tray')
                ->color('primary')
                ->modalHeading('Import Report Stock from Excel')
                ->modalDescription('Upload an Excel file to import report stock data. Make sure your file follows the correct format.')
                ->modalSubmitActionLabel('Import')
                ->modalWidth('lg')
                ->schema([
                    DatePicker::make('importReportDate')
                        ->label('Report Date')
                        ->required()
                        ->default(now()->format('Y-m-d'))
                        ->helperText('Select the date for this report stock import'),

                    FileUpload::make('importFile')
                        ->label('Excel File')
                        ->required()
                        ->acceptedFileTypes(['application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'application/vnd.ms-excel'])
                        ->maxSize(10240) // 10MB
                        ->helperText('Upload Excel file (.xlsx or .xls) with maximum size 10MB')
                        ->disk('local') // Change to local disk for better reliability
                        ->directory('temp-imports')
                        ->visibility('private')
                        ->storeFileNamesIn('original_filename') // Add this to track original filename
                        ->preserveFilenames() // Keep original filenames
                ])
                ->action(function (array $data) {
                    $this->processImport($data);
                }),

            Action::make('download_template')
                ->label('Download Template')
                ->icon('heroicon-o-arrow-down-tray')
                ->color('gray')
                ->url(fn () => route('report-stock.download-template'))
                ->openUrlInNewTab(),
        ];
    }

    public function getTitle(): string
    {
        return 'Stock Reports by Outlet';
    }

    public function getHeading(): string
    {
        return 'Stock Reports by Outlet';
    }

    public function getSubheading(): ?string
    {
        return 'Choose an outlet to view stock report dates and details.';
    }



    /**
     * Process the import from modal form
     */
    public function processImport(array $data): void
    {
        try {
            $reportDate = $data['importReportDate'];
            $uploadedFiles = $data['importFile'];

            if (empty($uploadedFiles)) {
                throw new \Exception('No file uploaded');
            }

            $uploadedFile = is_array($uploadedFiles) ? $uploadedFiles[0] : $uploadedFiles;

            // Validate the uploaded file path exists
            if (empty($uploadedFile) || !Storage::disk('local')->exists($uploadedFile)) {
                throw new \Exception('Uploaded file not found or invalid');
            }

            $filePath = Storage::disk('local')->path($uploadedFile);

            if (!file_exists($filePath)) {
                throw new \Exception('File path does not exist: ' . $filePath);
            }

            Notification::make()
                ->title('Import Started')
                ->body('Processing your Excel file. This may take a few moments...')
                ->info()
                ->duration(3000)
                ->send();

            // Use simple import to avoid hanging issues
            $import = new ReportStockImport($reportDate);
            Excel::import($import, $filePath);

            // Get import statistics
            $summary = $import->getImportSummary();
            $errors = $import->getErrors();

            // Build success message
            $message = "Import completed successfully!\n";
            $message .= "• Processed rows: {$summary['processed_rows']}\n";
            $message .= "• Products created: {$summary['products_created']}\n";
            $message .= "• Products updated: {$summary['products_updated']}\n";
            $message .= "• Outlet products created: {$summary['outlet_products_created']}\n";
            $message .= "• Outlet products updated: {$summary['outlet_products_updated']}\n";
            $message .= "• Report stock details created: {$summary['report_stock_details_created']}\n";
            $message .= "• Report stock details updated: {$summary['report_stock_details_updated']}";

            if (!empty($errors)) {
                $message .= "\n• Errors: " . count($errors);
            }

            if (!empty($summary['skipped_outlets'])) {
                $message .= "\n• Skipped outlets: " . implode(', ', $summary['skipped_outlets']);
            }

            Notification::make()
                ->title('Import Completed')
                ->body($message)
                ->success()
                ->duration(8000)
                ->send();

            // Show errors if any
            if (!empty($errors)) {
                $errorMessage = "Some rows had errors:\n";
                foreach (array_slice($errors, 0, 5) as $error) {
                    $errorMessage .= "Row {$error['row']}: {$error['error']}\n";
                }
                if (count($errors) > 5) {
                    $errorMessage .= "... and " . (count($errors) - 5) . " more errors";
                }

                Notification::make()
                    ->title('Import Errors')
                    ->body($errorMessage)
                    ->warning()
                    ->duration(10000)
                    ->send();
            }

            // Clean up uploaded file
            // if (is_array($uploadedFiles)) {
            //     foreach ($uploadedFiles as $file) {
            //         Storage::disk('local')->delete($file);
            //     }
            // } else {
            //     Storage::disk('local')->delete($uploadedFiles);
            // }

            Log::info('Import completed successfully before dispatch');
            // Refresh the table
            $this->dispatch('$refresh');
        } catch (\Exception $e) {
            Notification::make()
                ->title('Import Failed')
                ->body("An error occurred: {$e->getMessage()}")
                ->danger()
                ->duration(8000)
                ->send();

            // Clean up uploaded file on error
            if (isset($data['importFile'])) {
                $uploadedFiles = $data['importFile'];
                if (is_array($uploadedFiles)) {
                    foreach ($uploadedFiles as $file) {
                        Storage::disk('local')->delete($file);
                    }
                } else {
                    Storage::disk('local')->delete($uploadedFiles);
                }
            }
        }
    }






}



